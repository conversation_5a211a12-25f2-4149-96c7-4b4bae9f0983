请根据以下需求修改 `src/views/monitoring/alertcenter/components/HealthBoardV2.vue` 文件，实现健康看板表格的三级嵌套展示功能。后端接口文档位于 `docs/后端接口文档/healthBoardV2Api.md`。
可参考`src/views/monitoring/alertcenter/components/HealthBoard.vue` 文件中表格的展开方式进行三级三级行的展开

## 表格结构需求

### 1. 表头设计
- 保留现有表头：包含"告警来源"、"严重"、"警告"、"提示"、"子模块数"、"总数"字段
- 表头样式保持不变

### 2. 数据层级结构
- **一级行（告警来源）**：
  - 显示主要告警来源类别（如"自定义"、"腾讯云"等）
  - 展示各严重程度的告警数量统计
  - 显示子模块数量和总告警数
  - 点击可展开/折叠二级行

- **二级行（告警模块）**：
  - 显示具体告警模块名称（如"分支管理告警"、"基线分支反向合并通知"等）
  - 展示各严重程度的告警数量统计
  - 点击可展开/折叠三级行

- **三级行（告警对象）**：
  - 基于二级行展开，显示具体的告警对象（alertObject）
  - 显示告警状态（status）
  - 根据告警级别 severity 在对应的"严重"或"警告"列显示状态标识，红色对勾图标标注

### 3. 数据处理
- 利用后端接口 `healthBoardV2Api` 返回的 `alert_details` 数组数据
- 将 `alertObject` 和 `status` 信息展示在三级行中
- 根据 `status` 值确定显示在哪个严重程度列（严重/警告）

## 交互功能
- 实现行展开/折叠功能
- 保留现有的点击告警数量跳转功能
- 确保三级行的展开状态正确保存在 `expandedRows` 数组中

请确保代码实现与现有组件风格保持一致，并保留原有的时间筛选、自动刷新等功能。
