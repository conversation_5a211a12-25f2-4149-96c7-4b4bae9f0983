<template>
  <div class="history-alerts-view">
    <div>
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <div class="filter-row">
          <el-form-item class="time-range-item" label="">
            <div class="time-range-container">
              <el-radio-group
                v-model="timeRangeType"
                @change="handleTimeRangeChange"
              >
                <el-radio-button label="1d">1天</el-radio-button>
                <el-radio-button label="3d">3天</el-radio-button>
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="custom">自定义</el-radio-button>
              </el-radio-group>
              <el-date-picker
                v-if="timeRangeType === 'custom'"
                v-model="timeRange"
                :default-time="['00:00:00', '23:59:59']"
                class="custom-date-picker"
                end-placeholder="结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始时间"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="handleCustomTimeChange"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <statistics-cards
      :data="{
        statistics: chartData.statistics,
        total_all: chartData.total_all || 0,
      }"
    />

    <el-row class="chart-row">
      <el-col :span="24">
        <history-alerts-chart
          :data="chartData"
          :loading="loading"
          :time-range="timeRange"
        />
      </el-col>
    </el-row>

    <el-row class="chart-row">
      <el-col :span="24">
        <history-alerts-trend
          :loading="loading"
          :time-range="timeRange"
          :trend-data="trendData"
        />
      </el-col>
    </el-row>

    <el-row class="chart-row">
      <el-col :span="24">
        <el-card>
          <div class="stats-section">
            <div class="section-header">
              <h3>告警对象统计 (历史)</h3>
            </div>
            <alert-object-pie
              v-loading="loading"
              :data="alertObjectList"
              height="300px"
              layout="horizontal"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="24">
        <div class="table-filter">
          <el-form :inline="true" :model="queryParams" class="filter-form">
            <el-form-item label="级别">
              <el-radio-group
                v-model="queryParams.severity"
                size="small"
                @change="handleSeverityChange"
              >
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="critical">严重</el-radio-button>
                <el-radio-button label="warning">警告</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态">
              <el-radio-group
                v-model="queryParams.status"
                size="small"
                @change="handleStatusChange"
              >
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="OK">已恢复</el-radio-button>
                <el-radio-button label="ALARM">告警中</el-radio-button>
                <el-radio-button label="NO_DATA">无数据</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="来源">
              <el-radio-group
                v-model="queryParams.source"
                size="small"
                @change="handleSourceChange"
              >
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button
                  v-for="source in sourceList"
                  :key="source.source"
                  :label="source.source"
                >
                  {{ formatSource(source.source) }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>

        <div class="table-card">
          <el-table
            v-loading="loading"
            :data="tableData"
            height="calc(100vh - 400px)"
            style="width: 100%"
          >
            <el-table-column label="" prop="severity" width="60">
              <template #default="scope">
                <div class="severity-icon-wrapper">
                  <i
                    :class="getSeverityIcon(scope.row.severity)"
                    :style="{ color: getSeverityColor(scope.row.severity) }"
                    class="severity-icon"
                  ></i>
                </div>
              </template>
            </el-table-column>

            <el-table-column :min-width="180" label="来源" prop="source">
              <template #default="scope">
                <template
                  v-if="
                    scope.row.source === 'tencent_cloud' && scope.row.account_id
                  "
                >
                  <el-tag :type="getSourceType(scope.row.source)" size="small">
                    {{ formatSourceWithAccount(scope.row) }}
                  </el-tag>
                  <el-tag
                    v-if="
                      scope.row.source === 'tencent_cloud' && scope.row.labels
                    "
                    :effect="getTagEffect('namespace')"
                    class="label-tag"
                    size="small"
                  >
                    {{
                      scope.row.labels.namespace ||
                      scope.row.labels.product_show_name
                    }}
                  </el-tag>
                </template>
                <template v-else>
                  <el-tag :type="getSourceType(scope.row.source)" size="small">
                    {{ formatSource(scope.row.source) }}
                  </el-tag>
                </template>
              </template>
            </el-table-column>

            <el-table-column label="对象" prop="alert_object" min-width="150">
              <template #default="scope">
                <span v-if="getAlertObject(scope.row)" size="small" type="info">
                  {{ getAlertObject(scope.row) }}
                </span>
                <span v-else class="no-object">-</span>
              </template>
            </el-table-column>

            <el-table-column
              :min-width="200"
              label=""
              prop="summary"
              show-overflow-tooltip
            />
            <el-table-column
              :min-width="100"
              label="开始时间"
              prop="starts_at"
            />
            <el-table-column fixed="right" label="操作" width="120">
              <template #default="scope">
                <el-button
                  size="small"
                  type="text"
                  @click="handleDetailClick(scope.row)"
                >
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="[10, 20, 50, 100, 200]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import {
    getAlertCenterSources,
    getAlertsCenterHistory,
    getAlertsCenterTrend,
  } from '@/api/monitoring'
  import { debounce } from 'lodash'
  import dayjs from 'dayjs'
  import utc from 'dayjs/plugin/utc'
  import timezone from 'dayjs/plugin/timezone'
  import HistoryAlertsChart from './HistoryAlertsChart.vue'
  import HistoryAlertsTrend from './HistoryAlertsTrend.vue'
  import StatisticsCards from './StatisticsCards.vue'
  import AlertObjectPie from './AlertObjectPie.vue'

  dayjs.extend(utc)
  dayjs.extend(timezone)
  dayjs.tz.setDefault('Asia/Shanghai')

  export default {
    name: 'StatisticsBoard',
    components: {
      HistoryAlertsChart,
      HistoryAlertsTrend,
      StatisticsCards,
      AlertObjectPie,
    },
    data() {
      return {
        loading: false,
        timeRange: this.getDefaultTimeRange(),
        queryParams: {
          severity: '',
          status: '',
          source: '',
        },
        tableData: [],
        sourceList: [],
        currentPage: 1,
        pageSize: 50,
        total: 0,
        severityColorMap: {
          critical: 'danger',
          major: 'danger',
          warning: 'warning',
          resolved: 'success',
        },
        statusColorMap: {
          ALARM: 'warning',
          OK: 'success',
          NO_DATA: 'info',
        },
        accountColorMap: { PunkSong: 'primary', 微购科技2: 'success' },
        sourceTypeMap: {
          prometheus: 'warning',
          grafana: 'success',
          custom: 'info',
          tencent_cloud: 'primary',
          pinpoint: '#409EFF',
        },
        debouncedFetchData: null,
        timeRangeType: '7d',
        lastTimeRange: null,
        chartData: {
          alerts: [],
          statistics: {
            status_stats: {},
            severity_stats: {},
            source_stats: {},
          },
          total_all: 0,
        },
        trendData: {
          interval: 'hour',
          trend: [],
          summary: {
            total: 0,
            critical: 0,
            warning: 0,
            resolved: 0,
          },
        },
        alertObjectList: [],
      }
    },
    created() {
      this.debouncedFetchData = debounce(this.fetchData, 300)
      this.handleQuery()
    },
    mounted() {
      if (this.timeRange && this.timeRange.length === 2) {
        this.fetchData()
      }
    },
    methods: {
      getAlertObject(row) {
        if (!row || !row.labels) return null

        const checkNamespace = (keyword) =>
          row.labels.namespace?.includes(keyword) || false

        if (row.source === 'tencent_cloud') {
          const hasTke = checkNamespace('tke')
          const hasCdb = checkNamespace('cdb')
          const hasCes = checkNamespace('ces')

          const tkeFallbackFields = [
            'dim_instanceid',
            'dim_unInstanceId',
            'dim_instance_id',
            'dim_objId',
            'dim_objName',
            'target',
            'alert_object',
          ]

          let alertObject = hasTke
            ? row.labels.dim_objName || null
            : this.findFirstValidField(row.labels, tkeFallbackFields)

          if (alertObject && hasTke) {
            const podMatch = alertObject.match(/Pod名称:([^|]+)/)
            return podMatch?.[1]?.trim() || null
          }

          if (alertObject && hasCdb) return row.labels.dim_uInstanceId
          if (alertObject && hasCes) return row.labels.dim_cluster_name

          return alertObject
        }
        if (row.source === 'custom') {
          if (row.annotations?.extra_info?.jump_url) {
            try {
              const url = new URL(row.annotations.extra_info.jump_url)
              const searchParams = new URLSearchParams(url.search)
              let pathValue = searchParams.get('path')

              if (!pathValue && url.hash) {
                const hashParams = new URLSearchParams(
                  url.hash.split('?')[1] || ''
                )
                pathValue = hashParams.get('path')
              }
              return (
                pathValue || row.labels.path || row.labels.alert_object || null
              )
            } catch (error) {
              console.error('解析URL失败:', error)
            }
          }
          return row.labels.path || row.labels.alert_object || null
        }
        return row.labels.alert_object || null
      },
      findFirstValidField(obj, fields) {
        for (const field of fields) {
          if (obj[field] !== undefined && obj[field] !== null) {
            return obj[field]
          }
        }
        return null
      },
      getSeverityIcon(severity) {
        const iconMap = {
          critical: 'el-icon-error',
          warning: 'el-icon-warning',
          major: 'el-icon-warning-outline',
          resolved: 'el-icon-success',
        }
        return iconMap[severity] || 'el-icon-info'
      },

      getSeverityColor(severity) {
        const colorMap = {
          critical: '#F56C6C',
          warning: '#E6A23C',
          major: '#FF8C00',
          resolved: '#67C23A',
        }
        return colorMap[severity] || '#909399'
      },

      updateAlertObjectStats() {
        const objectMap = new Map()

        this.tableData.forEach((alert) => {
          const alertObject = this.getAlertObject(alert) || '未知对象'

          if (objectMap.has(alertObject)) {
            objectMap.set(alertObject, objectMap.get(alertObject) + 1)
          } else {
            objectMap.set(alertObject, 1)
          }
        })

        this.alertObjectList = Array.from(objectMap.entries()).map(
          ([alertObject, count]) => ({
            alertObject,
            count,
          })
        )
      },

      async fetchData() {
        this.loading = true
        try {
          if (!this.timeRange || this.timeRange.length !== 2) {
            console.warn(
              'fetchData called with invalid time range:',
              this.timeRange
            )
            this.handleTimeRangeChange(this.timeRangeType)
            if (!this.timeRange || this.timeRange.length !== 2) {
              this.$message.error('请选择有效的时间范围')
              this.loading = false
              return
            }
          }

          const params = {
            severity: this.queryParams.severity || null,
            source: this.queryParams.source || null,
            status: this.queryParams.status || null,
            start_time: this.formatTimeParam(this.timeRange[0]),
            end_time: this.formatTimeParam(this.timeRange[1]),
            page: this.currentPage,
            per_page: this.pageSize,
          }

          const response = await getAlertsCenterHistory(params)
          if (response.status === 'success') {
            this.tableData = response.data.alerts || []
            this.total = response.data.total || 0
            this.chartData = {
              alerts: response.data.alerts || [],
              statistics: response.data.statistics || {
                status_stats: {},
                severity_stats: {},
                source_stats: {},
              },
              total_all: response.total_all || 0,
            }

            this.updateAlertObjectStats()
          } else {
            throw new Error(response.message || '获取历史告警数据失败')
          }
        } catch (error) {
          console.error('获取历史告警数据失败:', error)
          this.$message.error(error.message || '获取历史告警数据失败')
          this.tableData = []
          this.total = 0
          this.chartData.total_all = 0
          this.alertObjectList = []
        } finally {
          this.loading = false
        }
      },

      getTagEffect(key) {
        return ['alertStatus', 'severity', 'type'].includes(key)
          ? 'light'
          : 'plain'
      },
      formatISODate(date) {
        if (!date) return ''
        const d = date instanceof Date ? date : new Date(date)
        if (isNaN(d.getTime())) return ''
        return d.toISOString().slice(0, 19).replace('T', ' ')
      },
      getSeverityType(severity) {
        return this.severityColorMap[severity] || 'info'
      },
      getStatusType(status) {
        return this.statusColorMap[status] || 'info'
      },
      getSourceType(source) {
        return this.sourceTypeMap[source] || 'info'
      },
      getAccountType(accountId) {
        return this.accountColorMap[accountId] || 'default'
      },
      formatSeverity(severity) {
        const map = { critical: '严重', warning: '警告', resolved: '已恢复' }
        return map[severity] || severity
      },
      formatStatus(status) {
        const map = {
          ALARM: '告警中',
          OK: '已恢复',
          NO_DATA: '无数据',
          NO_CONF: '未配置',
        }
        return map[status] || status
      },
      formatSource(source) {
        const map = {
          tencent_cloud: '腾讯云',
          pinpoint: 'Pinpoint',
          grafana: 'Grafana',
          prometheus: 'Prometheus',
          custom: '自定义',
        }
        return map[source] || source
      },
      formatSourceWithAccount(alert) {
        if (alert.source === 'tencent_cloud' && alert.account_id) {
          const accountName = alert.account_id.split('|')[0]
          return `腾讯云 ${accountName}`
        }
        return this.formatSource(alert.source)
      },
      resetQuery() {
        this.queryParams = {
          severity: '',
          status: '',
          source: '',
        }
        this.timeRangeType = '7d'
        this.lastTimeRange = null
        this.timeRange = this.getDefaultTimeRange()
        this.handleQuery()
      },

      getDefaultTimeRange() {
        const end = dayjs().tz()
        const start = end.subtract(7, 'day')
        return [
          start.format('YYYY-MM-DD HH:mm:ss'),
          end.format('YYYY-MM-DD HH:mm:ss'),
        ]
      },

      handleTimeRangeChange(type) {
        if (type === 'custom') {
          if (this.lastTimeRange) {
            this.timeRange = [...this.lastTimeRange]
          }
          return
        }

        const end = dayjs().tz()
        const days = parseInt(type.replace('d', ''))
        const start = end.subtract(days, 'day')

        this.timeRange = [
          start.format('YYYY-MM-DD HH:mm:ss'),
          end.format('YYYY-MM-DD HH:mm:ss'),
        ]

        this.lastTimeRange = [...this.timeRange]

        this.handleQuery()
      },
      handleCustomTimeChange(val) {
        if (val && val.length === 2) {
          this.timeRange = val
          this.lastTimeRange = [...val]
          this.handleQuery()
        }
      },
      async handleQuery() {
        if (!this.timeRange || this.timeRange.length !== 2) {
          this.$message.warning('请选择历史告警的时间范围')
          return
        }
        try {
          await Promise.all([
            this.fetchData(),
            this.fetchAlertSources(),
            this.fetchTrendData(),
          ])
        } catch (error) {
          console.error('查询失败:', error)
          this.$message.error('查询失败，请重试')
        }
      },

      handleSizeChange(val) {
        this.pageSize = val
        this.currentPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.currentPage = val
        this.fetchData()
      },
      handleDetailClick(row) {
        this.$emit('show-detail', row)
      },
      async handleFilterChange() {
        this.currentPage = 1
        await this.handleQuery()
      },
      async handleSeverityChange() {
        this.currentPage = 1
        await this.handleQuery()
      },
      async handleStatusChange() {
        this.currentPage = 1
        await this.handleQuery()
      },
      async handleSourceChange() {
        this.currentPage = 1
        await this.handleQuery()
      },

      formatTimeParam(dateStr) {
        if (!dateStr) return ''

        return dateStr.replace(/\+/g, ' ')
      },
      async fetchAlertSources() {
        try {
          let startTime, endTime

          if (this.timeRange && this.timeRange.length === 2) {
            startTime = this.formatTimeParam(this.timeRange[0])
            endTime = this.formatTimeParam(this.timeRange[1])
          } else {
            const end = dayjs().tz()
            const start = end.startOf('day')
            startTime = start.format('YYYY-MM-DD HH:mm:ss')
            endTime = end.format('YYYY-MM-DD HH:mm:ss')
          }

          const params = {
            start_time: startTime,
            end_time: endTime,
            status: 'false',
          }

          const response = await getAlertCenterSources(params)
          if (
            response.status === 'success' &&
            response.data &&
            response.data.sources
          ) {
            this.sourceList = response.data.sources.map((item) => ({
              source: item.source,
              count: item.count,
            }))
          } else {
            throw new Error(response.message || '获取告警来源数据失败')
          }
        } catch (error) {
          console.error('获取告警来源失败:', error)
          this.$message.error(error.message || '获取告警来源失败')
          this.sourceList = []
        }
      },
      async fetchTrendData() {
        try {
          const params = {
            start_time: this.formatTimeParam(this.timeRange[0]),
            end_time: this.formatTimeParam(this.timeRange[1]),
          }

          const response = await getAlertsCenterTrend(params)
          if (response.status === 'success') {
            this.trendData = response.data
          } else {
            throw new Error(response.message || '获取告警趋势数据失败')
          }
        } catch (error) {
          console.error('获取告警趋势数据失败:', error)
          this.$message.error(error.message || '获取告警趋势数据失败')
          this.trendData = {
            interval: 'hour',
            trend: [],
            summary: {
              total: 0,
              critical: 0,
              warning: 0,
              resolved: 0,
            },
          }
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .history-alerts-view {
    padding: 8px 5px;
  }

  .chart-row {
    margin-bottom: 20px;
  }

  .filter-card {
    margin-bottom: 15px;
  }

  .table-filter {
    margin-top: 20px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    // border-bottom: 1px solid #ebeef5;

    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 0;
      }
    }
  }

  .table-card {
    background-color: #fff;
    padding: 0px;
    // border-radius: 4px;
    // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: calc(100vh - 310px);
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    .el-table {
      flex-grow: 1;
      margin-bottom: 0px;
    }

    .pagination-container {
      padding: 0;
      margin: 0;
      height: 15px;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-pagination {
        padding: 0;
        margin: 0;
      }
    }
  }

  .pagination-container {
    text-align: right;
    margin-top: auto;
  }

  .filter-form .filter-row {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  .filter-form .el-form-item {
    margin-right: 15px;
    margin-bottom: 5px;
  }

  .time-range-item .el-date-editor {
    width: 380px !important;
    margin-left: 10px;
  }

  ::v-deep .el-tag {
    border-radius: 4px;
    padding: 0 10px;
    height: 24px;
    line-height: 24px;

    &.el-tag--warning {
      background-color: #ffab2e;
      border-color: #ffab2e;
      color: white;

      &:hover {
        background-color: #ffab2e;
        border-color: #ffab2e;
        color: white;
      }
    }

    &.el-tag--danger {
      background-color: #fe495a;
      border-color: #fe495a;
      color: white;

      &:hover {
        background-color: #fe495a;
        border-color: #fe495a;
        color: white;
      }
    }
  }

  /* 告警级别图标样式 */
  .severity-icon-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    .severity-icon {
      font-size: 18px;
      font-weight: bold;
    }
  }

  /* 对象列样式 */
  .no-object {
    color: #c0c4cc;
    font-style: italic;
  }

  /* 统计部分样式 */
  .stats-section {
    background-color: #fff;
    border-radius: 4px;
    padding: 10px;
    height: 100%;

    .section-header {
      margin-bottom: 10px;
      padding-bottom: 10px;

      h3 {
        font-size: 16px;
        margin: 0;
        color: #303133;
      }
    }
  }

  ::v-deep .el-table {
    font-size: 14px;

    .el-table__header th {
      font-size: 14px;
      font-weight: bold;
      color: #606266;
    }

    .el-table__body td {
      font-size: 14px;
    }
  }

  .time-range-container {
    display: flex;
    align-items: center;
    gap: 16px;

    .custom-date-picker {
      width: 400px;
    }

    .el-radio-group {
      margin-right: 16px;
    }
  }

  @media (max-width: 768px) {
    .table-card {
      height: 500px;
    }
    .time-range-item .el-date-editor {
      width: 100% !important;
    }
  }
</style>
