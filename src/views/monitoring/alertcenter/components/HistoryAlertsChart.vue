<template>
  <div class="history-alerts-chart">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div class="card-header">
            <h5>来源分布</h5>
          </div>
          <div ref="sourceChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div class="card-header">
            <h5>状态分布</h5>
          </div>
          <div ref="statusChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="chart-card">
          <div class="card-header">
            <h5>级别分布</h5>
          </div>
          <div ref="severityChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'HistoryAlertsChart',
    props: {
      data: {
        type: Object,
        required: true,
        default: () => ({
          alerts: [],
          statistics: {
            status_stats: {},
            severity_stats: {},
            source_stats: {},
          },
        }),
      },
      timeRange: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        sourceChart: null,
        statusChart: null,
        severityChart: null,
      }
    },
    watch: {
      data: {
        handler() {
          this.updateChart()
        },
        deep: true,
      },
      timeRange: {
        handler() {
          this.updateChart()
        },
        deep: true,
      },
    },
    mounted() {
      this.initChart()
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
      this.sourceChart?.dispose()
      this.statusChart?.dispose()
      this.severityChart?.dispose()
    },
    methods: {
      initChart() {
        this.sourceChart = echarts.init(this.$refs.sourceChart)
        this.statusChart = echarts.init(this.$refs.statusChart)
        this.severityChart = echarts.init(this.$refs.severityChart)
        this.updateChart()
      },

      updateChart() {
        const { statistics } = this.data

        const statusData = this.processStatusData(statistics)
        const severityData = this.processSeverityData(statistics)
        const sourceData = this.processSourceData(statistics)

        const baseOptions = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)',
          },
          legend: {
            orient: 'horizontal',
            bottom: -5,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10,
            textStyle: {
              fontSize: 12,
              color: '#606266',
            },
          },
          series: [
            {
              type: 'pie',
              radius: ['45%', '70%'],
              center: ['50%', '45%'],
              avoidLabelOverlap: true,
              labelLayout: {
                hideOverlap: true,
                moveOverlap: 'shiftY',
              },
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: true,
                position: 'outside',
                formatter: (params) => {
                  const percent = params.percent
                  const isSmall = percent < 15
                  return isSmall
                    ? `{a|${params.name}}\n{b|${params.percent}%}`
                    : `{b|${params.percent}%}\n{a|${params.name}}`
                },
                rich: {
                  a: {
                    fontSize: 12,
                    lineHeight: 16,
                    color: '#606266',
                  },
                  b: {
                    fontSize: 12,
                    lineHeight: 16,
                    color: '#303133',
                    fontWeight: 'bold',
                  },
                },
                padding: [0, 0, 0, 0],
                minMargin: 5,
                distanceToLabelLine: 5,
              },
              labelLine: {
                show: true,
                length: 15,
                length2: 10,
                minTurnAngle: 45,
                maxSurfaceAngle: 80,
                smooth: 0.2,
                lineStyle: {
                  width: 1,
                  type: 'solid',
                },
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 12,
                  fontWeight: 'bold',
                },
              },
            },
          ],
        }

        const setChartOption = (chart, data, colors, name) => {
          const sortedData = [...data].sort((a, b) => b.value - a.value)
          chart.setOption({
            ...baseOptions,
            series: [
              {
                ...baseOptions.series[0],
                name,
                data: sortedData,
                color: colors,
                clockwise: true,
                startAngle: 90,
                minAngle: 5,
                minShowLabelAngle: 3,
              },
            ],
          })
        }

        setChartOption(
          this.sourceChart,
          sourceData,
          ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399'],
          '告警来源'
        )

        setChartOption(
          this.statusChart,
          statusData,
          ['#F56C6C', '#67C23A', '#909399'],
          '告警状态'
        )

        setChartOption(
          this.severityChart,
          severityData,
          ['#F56C6C', '#E6A23C', '#67C23A'],
          '告警级别'
        )
      },

      processStatusData(statistics) {
        const statusStats = statistics?.status_stats || {}
        return Object.entries(statusStats).map(([status, count]) => ({
          name: this.formatStatus(status),
          value: count,
        }))
      },

      processSeverityData(statistics) {
        const severityStats = statistics?.severity_stats || {}
        return Object.entries(severityStats).map(([severity, count]) => ({
          name: this.formatSeverity(severity),
          value: count,
        }))
      },

      processSourceData(statistics) {
        const sourceStats = statistics?.source_stats || {}
        return Object.entries(sourceStats).map(([source, count]) => ({
          name: this.formatSource(source),
          value: count,
        }))
      },

      formatStatus(status) {
        const map = {
          ACTIVE: '告警中',
          RESOLVED: '已恢复',
          ALARM: '告警中',
          OK: '已恢复',
          NO_DATA: '无数据',
          NO_CONF: '未配置',
        }
        return map[status] || status
      },

      formatSeverity(severity) {
        const map = { critical: '严重', warning: '警告', resolved: '已恢复' }
        return map[severity] || severity
      },

      formatSource(source) {
        const map = {
          tencent_cloud: '腾讯云',
          pinpoint: 'Pinpoint',
          grafana: 'Grafana',
          prometheus: 'Prometheus',
          custom: '自定义',
        }
        return map[source] || source
      },

      handleResize() {
        this.sourceChart?.resize()
        this.statusChart?.resize()
        this.severityChart?.resize()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .history-alerts-chart {
    .chart-card {
      transition: all 0.3s;
      height: 100%;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .card-header {
        margin-bottom: 15px;

        h5 {
          margin: 0;
          font-size: 14px;
          color: #303133;
          font-weight: 500;
          text-align: center;
        }
      }

      .chart-container {
        height: 240px;
        width: 100%;
      }
    }
  }
</style>
