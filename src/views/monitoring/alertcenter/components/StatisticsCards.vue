<template>
  <div class="statistics-cards">
    <el-row :gutter="20">
      <el-col v-for="(item, index) in statisticsItems" :key="index" :span="6">
        <el-card
          shadow="hover"
          :body-style="{ padding: '15px' }"
          class="stat-card"
        >
          <div class="stat-content">
            <div class="stat-icon" :class="item.iconClass">
              <i :class="item.icon"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">{{ item.title }}</div>
              <div class="stat-value">{{ item.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  export default {
    name: 'StatisticsCards',
    props: {
      data: {
        type: Object,
        required: true,
        default: () => ({
          statistics: {
            status_stats: {},
            severity_stats: {},
            source_stats: {},
          },
          total_all: 0,
        }),
      },
    },
    computed: {
      statisticsItems() {
        const { statistics, total_all } = this.data
        const severityStats = statistics?.severity_stats || {}
        const statusStats = statistics?.status_stats || {}

        const total = total_all || 0

        const critical = severityStats.critical || 0

        const warning = severityStats.warning || 0

        const resolved = statusStats.OK || 0

        return [
          {
            title: '总告警数',
            value: total,
            icon: 'el-icon-warning',
            iconClass: 'total',
          },
          {
            title: '严重',
            value: critical,
            icon: 'el-icon-error',
            iconClass: 'critical',
          },
          {
            title: '警告',
            value: warning,
            icon: 'el-icon-warning',
            iconClass: 'warning',
          },
          {
            title: '已恢复',
            value: resolved,
            icon: 'el-icon-success',
            iconClass: 'resolved',
          },
        ]
      },
    },
  }
</script>

<style lang="scss" scoped>
  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
    }

    .stat-content {
      display: flex;
      align-items: center;
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: white;
      }

      &.total {
        background-color: #409eff;
      }

      &.critical {
        background-color: #f56c6c;
      }

      &.warning {
        background-color: #e6a23c;
      }

      &.resolved {
        background-color: #67c23a;
      }
    }

    .stat-info {
      flex: 1;
    }

    .stat-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 5px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
    }
  }
</style>
