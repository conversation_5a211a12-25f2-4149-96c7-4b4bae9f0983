<template>
  <el-dialog
    :close-on-click-modal="isedit"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="业务线" prop="flow_uuid">
            <el-select
              v-model="form.bizline"
              filterable
              placeholder="请选择"
              @change="handleChange"
            >
              <el-option
                v-for="item in allBizlines"
                :key="item.tapd_id"
                :label="item.tapd_name"
                :value="item.tapd_name"
              >
                <span style="float: left">{{ item.tapd_name }}</span>
                <span style="float: right; color: #8492a6; font-size: 8px">
                  {{ item.tapd_id }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="版本" prop="version_code">
            <el-select
              v-model="form.version_code"
              :disabled="isVersionCodeEmpty"
              allow-create
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in allIteration"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="别名" placeholder="环境别名" prop="pre_alias">
            <el-input
              v-model.trim="form.pre_alias"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="时间" prop="valid_time">
            <el-date-picker
              v-model="form.valid_time"
              :picker-options="pickerOptions"
              align="right"
              placeholder="计划销毁时间"
              type="date"
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col
          v-for="(role, index) in roleUsers"
          :key="index"
          :span="24 / roleUsers.length"
        >
          <el-form-item :label="role.name" :prop="role.key">
            <el-select v-model="form[role.key]" multiple placeholder="请选择">
              <el-option
                v-for="user in role.users"
                :key="user.username"
                :label="user.displayname"
                :value="user.username"
              >
                <span style="float: left">{{ user.displayname }}</span>
                <span style="float: right; color: #8492a6; font-size: 8px">
                  {{ user.username }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="申请原因" prop="memo">
            <el-input
              v-model.trim="form.memo"
              :rows="4"
              autocomplete="off"
              spellcheck="false"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row style="text-align: right">
        <div class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button
            type="primary"
            @click="isedit ? updateApply() : saveEnvPut()"
          >
            {{ isedit ? '更新信息' : '提交申请' }}
          </el-button>
        </div>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
  import { getBizlines, getIterations } from '@/api/tapd'
  import { actionEnv, postEnv } from '@/api/presm'

  export default {
    name: 'PreManagementEdit',
    props: {
      allUserList: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        form: {
          bizline: '',
          pre_alias: '',
          valid_time: '',
          status: 1,
          version_code: '',
          tester: '',
          product: '',
          operation: '',
          memo: '',
        },
        userLists: [],
        isedit: false,
        allBizlines: [],
        allIteration: [],
        roleUsers: [
          { name: '测试', key: 'tester', users: [] },
          { name: '产品', key: 'product', users: [] },
        ],
        typeOptions: [
          {
            value: '0',
            label: '开发联调',
            disabled: true,
          },
          {
            value: '1',
            label: '系统测试',
          },
          {
            value: '3',
            label: '灰度验证',
            disabled: true,
          },
        ],

        rules: {
          version_code: [
            {
              required: true,
              trigger: 'blur',
              message: '请选择或者填写迭代版本号',
            },
          ],
          pre_alias: [{ required: true, message: '环境别名' }],
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() <= Date.now()
          },
          shortcuts: [
            {
              text: '三天',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 3)
                picker.$emit('pick', new Date())
              },
            },
            {
              text: '一周',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 7)
                picker.$emit('pick', date)
              },
            },
            {
              text: '两周',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 14)
                picker.$emit('pick', date)
              },
            },
          ],
        },
        currentUser: '',
        isAdmin: false,
        title: '',
        dialogFormVisible: false,
      }
    },
    computed: {
      isVersionCodeEmpty() {
        if (this.form.version_code && this.isedit == true) {
          return true
        } else {
          return false
        }
      },
    },
    mounted() {},
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '申请新环境'
          this.isedit = false
        } else {
          this.title = '更新信息'
          this.isedit = true
          this.form = {
            ...row,
          }
          this.form.tester = row.tester ? row.tester.split(',') : []
          this.form.product = row.product ? row.product.split(',') : []
        }

        this.fetchData()
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()

        this.dialogFormVisible = false
      },
      async handleChange() {
        const { data } = await getIterations({ line_name: this.form.bizline })
        this.allIteration = data
        setTimeout(() => {}, 600)
      },
      async fetchData() {
        const { data } = await getBizlines()
        this.allBizlines = data

        const groupedUsers = this.allUserList.reduce((acc, user) => {
          user.role.forEach((role) => {
            if (['产品', '测试'].includes(role)) {
              const groupedUser = {
                username: user.username,
                displayname: user.displayname,
              }

              if (!acc[role]) {
                acc[role] = []
              }
              acc[role].push(groupedUser)
            }
          })
          return acc
        }, {})

        this.roleUsers[0].users = groupedUsers['测试'] || []
        this.roleUsers[1].users = groupedUsers['产品'] || []

        setTimeout(() => {}, 300)
      },

      updateApply() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            const updateData = { ...this.form }

            updateData.tester = updateData.tester.join(',')
            updateData.product = updateData.product.join(',')

            const { msg } = await actionEnv(
              updateData.pre_uuid,
              updateData,
              'update'
            )
            this.$baseMessage(msg, 'success')
            this.close()
            this.$emit('fetch-data')
          } else {
            return false
          }
        })
      },

      saveEnvPut() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            this.form.tester = this.form.tester.join(',')
            this.form.product = this.form.product.join(',')
            console.log('新增环境 format', this.form)
            const { msg } = await postEnv(this.form)
            this.$baseMessage(msg, 'success')
            this.close()
            this.$emit('fetch-data')
          } else {
            return false
          }
        })
      },
    },
  }
</script>
