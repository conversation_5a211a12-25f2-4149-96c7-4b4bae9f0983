<template>
  <div class="presm-container">
    <el-drawer
      :visible.sync="jenkins_drawer"
      direction="rtl"
      size="50%"
      @closed="handleDrawerClose"
      @opened="handleDrawerOpen"
    >
      <template #title>
        <div style="margin-bottom: 20px; margin-left: 10px; font-weight: bold">
          <p>构建日志</p>
          <p>项目名: {{ jenkinsInfo.project }}</p>
          <p>构建号: {{ jenkinsInfo.buildno }}</p>
        </div>
      </template>

      <pre
        v-for="text in progressiveText"
        :key="text"
        style="margin-left: 20px"
        >{{ text }}</pre
      >
      <el-button
        v-if="logloading"
        :loading="true"
        style="margin-left: 20px"
      ></el-button>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :lg="14" :md="14" :sm="24" :xl="14" :xs="24">
        <el-card shadow="never">
          <el-descriptions
            :column="3"
            border
            class="margin-top"
            size="medium"
            title="环境基本信息"
          >
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-box"></i>
                预发布ID
              </template>
              {{ queryForm.pre_uuid }}
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-thumb"></i>
                预发布别名
              </template>
              {{ detailInfo.pre_alias }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-trophy"></i>
                迭代版本
              </template>
              {{ detailInfo.version_code }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-time"></i>
                创建时间
              </template>
              {{ detailInfo.create_time }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                负责人
              </template>
              {{ getDisplayName(detailInfo.applicant) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                测试
              </template>
              {{ getDisplayName(detailInfo.tester) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                产品
              </template>
              {{ getDisplayName(detailInfo.product) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                业务线
              </template>
              <el-tag size="medium">{{ detailInfo.bizline }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-edit"></i>
                操作
              </template>
              <el-button type="primary" @click="handleEnvEdit(detailInfo)">
                编辑
              </el-button>
              <env-edit
                ref="envEdit"
                :all-user-list="allUserList"
                @fetch-data="fetchData"
              ></env-edit>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <el-col
        :lg="10"
        :md="10"
        :sm="24"
        :xl="10"
        :xs="24"
        class="album-info-container"
      >
        <el-card ref="trafficCard" class="traffic-card">
          <div slot="header" class="header">
            <div>
              <span style="font-size: 16px; font-weight: bold">流量统计：</span>
              <span>{{ TrafficTimeRange }}</span>
            </div>
            <div class="refresh-container">
              <el-switch
                v-model="autoRefresh"
                active-text="刷新/10s"
                inactive-text="关闭"
                inline-prompt
                @change="toggleAutoRefresh"
              ></el-switch>
            </div>
          </div>
          <div class="body-div">
            <el-row
              v-if="preUuidTraffic && preUuidTraffic.length > 0"
              v-loading="loading"
              :element-loading-background="'rgba(255, 255, 255, 0.7)'"
              :gutter="20"
              class="traffic-el-row"
              element-loading-spinner="el-icon-loading"
              element-loading-text="正在加载..."
            >
              <el-col
                v-for="row in preUuidTraffic"
                :key="row.id"
                :span="computedTraffic"
              >
                <el-card style="margin: -15px 0px">
                  <div style="display: inline-block">
                    <el-statistic
                      :precision="0"
                      :title="row.type"
                      group-separator=","
                    >
                      <span slot="suffix">
                        {{ row.sum_of_count }}
                      </span>
                    </el-statistic>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <el-empty v-else description="暂无数据" />
          </div>
        </el-card>
      </el-col>

      <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
        <el-tabs v-model="activeTab" type="card" @tab-click="handleTabchange">
          <el-tab-pane name="services">
            <span slot="label">
              <i class="el-icon-setting"></i>
              <b>服务配置</b>
            </span>
            <vab-query-form>
              <vab-query-form-left-panel :span="12">
                <el-button
                  icon="el-icon-edit"
                  type="primary"
                  @click="handleManage"
                >
                  增加服务
                </el-button>
                <el-button
                  icon="el-icon-upload"
                  type="warning"
                  @click="handleBuild"
                >
                  批量发布
                </el-button>
                <el-button
                  icon="el-icon-upload"
                  type="primary"
                  @click="handleRefresh"
                >
                  重新加载
                </el-button>
                <el-dropdown v-if="false" @command="handleCommand">
                  <el-button icon="el-icon-plus" type="primary">
                    更多功能
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="cc" icon="el-icon-document-copy">
                      配置对比
                    </el-dropdown-item>
                    <el-dropdown-item command="ll" icon="el-icon-view">
                      构建日志
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </vab-query-form-left-panel>
              <vab-query-form-right-panel :span="6">
                <el-form
                  :inline="true"
                  :model="queryForm"
                  @submit.native.prevent
                >
                  <el-form-item>
                    <el-input
                      v-model.trim="queryForm.keyword"
                      clearable
                      placeholder="请输入查询关键字"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      icon="el-icon-search"
                      type="primary"
                      @click="queryData"
                    >
                      查询
                    </el-button>
                  </el-form-item>
                </el-form>
              </vab-query-form-right-panel>
            </vab-query-form>
            <el-row :gutter="24">
              <el-col :span="18">
                <el-card>
                  <el-table
                    v-loading="listLoading"
                    :data="filteredList"
                    :element-loading-text="elementLoadingText"
                    @selection-change="setSelectRows"
                    @sort-change="handleSortChange"
                  >
                    <el-table-column
                      show-overflow-tooltip
                      type="selection"
                    ></el-table-column>
                    <el-table-column
                      label="服务名"
                      prop="name"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      label="项目名称"
                      prop="project"
                      show-overflow-tooltip
                    ></el-table-column>

                    <el-table-column label="状态" show-overflow-tooltip>
                      <template #default="{ row }">
                        <el-tag :type="row.status | statusType">
                          {{ row.status | statusMap }}
                        </el-tag>
                      </template>
                    </el-table-column>

                    <el-table-column
                      label="分支"
                      prop="project_branch"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      label="时间"
                      prop="latest_build_time"
                      show-overflow-tooltip
                      sortable
                    ></el-table-column>
                    <el-table-column
                      label="次数"
                      prop="publish_count"
                      min-width="50"
                      show-overflow-tooltip
                      sortable
                    ></el-table-column>

                    <el-table-column
                      label="操作"
                      show-overflow-tooltip
                      min-width="150"
                    >
                      <template #default="{ row }">
                        <el-button type="text" @click="handleBuild(row)">
                          构建
                        </el-button>

                        <el-button
                          v-if="row.status > 1"
                          type="text"
                          @click="handleGetbuildlog(row)"
                        >
                          构建日志
                        </el-button>

                        <el-button
                          v-if="row.status === 1 && !row.name.includes('前端')"
                          type="text"
                        >
                          <a
                            :href="getKibanaUrl(row)"
                            rel="noopener noreferrer"
                            target="_blank"
                          >
                            Kibana查询
                          </a>
                        </el-button>

                        <el-button
                          v-if="row.status === 1 && !row.name.includes('前端')"
                          type="text"
                        >
                          <a
                            :href="getServiceUrl(row)"
                            rel="noopener noreferrer"
                            target="_blank"
                          >
                            访问
                          </a>
                        </el-button>

                        <el-button type="text" @click="handleDelete(row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-pagination
                    :current-page="queryForm.pageNo"
                    :layout="layout"
                    :page-size="queryForm.pageSize"
                    :total="total"
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  ></el-pagination>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-alert :closable="false" show-icon title="Tips:" type="info">
                  <template #default>
                    <div class="ci-alert-list">
                      1.通过服务管理选择本次迭代版本涉及的服务和分支。
                    </div>
                    <div class="ci-alert-list">
                      2.系统测试结束后，需确认并同步新增配置后方可进入下一阶段。
                    </div>
                  </template>
                </el-alert>

                <el-card class="latest-dynamics">
                  <div slot="header" class="card-header">
                    <span>动态信息</span>
                  </div>
                  <div class="scroll-content">
                    <seamless-scroll
                      ref="seamlessScroll"
                      :data="activities"
                      :scroll-options="scrollOptions"
                      :hover-stop="true"
                      :is-reset="false"
                      class="dynamic-info"
                    >
                      <template #default="">
                        <el-timeline>
                          <el-timeline-item
                            v-for="(activity, index) in activities"
                            :key="index"
                            :color="activity.color"
                            :timestamp="activity.create_time"
                            placement="top"
                          >
                            <div>
                              {{ getDisplayName(activity.applicant) }}
                              {{ activity.step_name }}
                            </div>
                            <div>
                              结果：
                              <span :style="{ color: activity.color }">
                                {{ activity.step_result }}
                              </span>
                            </div>
                          </el-timeline-item>
                        </el-timeline>
                      </template>
                    </seamless-scroll>
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <edit ref="edit" @fetch-data="fetchData"></edit>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import VabChart from '@/plugins/echarts'
  import Edit from './components/projectManagementEdit.vue'
  import EnvEdit from '../components/PreManagementEdit.vue'
  import AlbumInfo from '../components/AlbumInfo.vue'
  import {
    deleteService,
    EnvServieLogs,
    getEnv,
    getPreJenkinsBuildInfo,
    getPreTraffic,
    getServices,
    putService,
  } from '@/api/presm'
  import SeamlessScroll from 'vue-seamless-scroll'
  import { EnvRule } from '../../../../api/presm'
  import {
    getJksProgressiveText,
    perfPodlist,
    sandboxPodlist,
  } from '@/api/ciWorkflow'

  export default {
    name: 'PreManagement',
    components: { VabChart, Edit, AlbumInfo, EnvEdit, SeamlessScroll },
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'info',
          1: 'success',
          2: 'warning',
          3: 'danger',
          4: 'warning',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '未发布',
          1: '运行中',
          2: '构建中',
          3: '构建失败',
          4: '部署中',
        }
        return statusMap[status]
      },
      bindstatusType(status) {
        const statusMap = {
          0: 'success',
          1: 'warning',
        }
        return statusMap[status]
      },
      bindstatusMap(status) {
        const statusMap = {
          0: '空闲中',
          1: '已绑定',
        }
        return statusMap[status]
      },
      podstatusType(row) {
        if (row.conditions) {
          if (
            row.conditions[0].status === 'True' &&
            row.conditions[1].status === 'True' &&
            row.conditions[2].status === 'True' &&
            row.conditions[3].status === 'True'
          ) {
            return 'success'
          } else {
            return 'danger'
          }
        } else {
          return 'danger'
        }
      },
    },
    data() {
      return {
        autoRefresh: true,
        intervalId: null,
        loading: false,
        scrollOptions: {
          step: 0.15,
          limitMoveNum: 2,
          hoverStop: true,
          direction: 1,
          waitTime: 2000,
          autoPlay: {
            enable: true,
            interval: 3500,
          },
          switchDelay: 500,
          singleHeight: 30,
        },
        applicantDisplayNameMap: [],
        displayname: {
          applicant: '',
          tester: '',
          product: '',
        },
        aliasDialogVisible: false,
        domainData: [],
        timer: null,
        progressiveText: ['构建任务排队中……'],
        jenkins_drawer: false,
        reverse: true,
        logloading: true,
        getTapd: false,

        jenkinsInfo: {
          project: 'UAT-后端',
          buildno: 37,
          start: 0,
        },
        albumid: '',

        activeTab: 'services',
        listLoading: true,
        podLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        quality: 5.0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        detailInfo: {},
        envType: '系统测试',
        queryForm: {
          pre_uuid: '',
          pre_alias: '',
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
        queryFormBackup: {},
        list: [],
        originalList: [],
        filteredList: [],
        podtotal: 0,
        podForm: {
          namespace: '',
          pageNum: 1,
          pageSize: 10,
          keyword: '',
        },
        podlist: [],
        activities: null,
        preUuidTraffic: [],
        TrafficTimeRange: '',
      }
    },
    computed: {
      allUserList() {
        return this.$store.state.consts.allUserList
      },
      computedTraffic() {
        return 24 / this.preUuidTraffic
      },
    },
    watch: {
      detailInfo: {
        handler() {
          this.setDisplayNames()
        },
        deep: true,
      },
    },
    created() {
      this.listLoading = false
      this.queryForm.pre_uuid = this.$route.params.pre_uuid
      this.podForm.namespace = this.$route.params.pre_uuid
      this.queryFormBackup = JSON.parse(JSON.stringify(this.queryForm))
      if (this.$route.meta.title) {
        document.title = `${this.$route.meta.title}-${
          this.$route.params.pre_alias || this.$route.params.pre_uuid
        }`
      }
    },

    beforeDestroy() {
      clearInterval(this.timer)
      this.stopAutoRefresh()
    },
    mounted() {
      this.fetchData()
      this.getPreGray()
      this.getTraffic()
    },

    methods: {
      toggleAutoRefresh(checked) {
        if (checked) {
          this.startAutoRefresh()
        } else {
          this.stopAutoRefresh()
        }
      },
      startAutoRefresh() {
        this.intervalId = setInterval(() => {
          this.getTraffic()
        }, 10000)
      },
      stopAutoRefresh() {
        if (this.intervalId) {
          clearInterval(this.intervalId)
          this.intervalId = null
        }
      },

      getDisplayName(value) {
        if (!value) {
          return ''
        }

        const names = value.split(',')
        const displayNames = names
          .map((name) => {
            return (
              this.$store.state.consts.applicantDisplayNameMap[name] || name
            )
          })
          .join(', ')
        return displayNames || ''
      },

      setDisplayNames() {
        this.displayname.applicant = this.getDisplayName(
          this.detailInfo.applicant
        )
        this.displayname.tester = this.getDisplayName(this.detailInfo.tester)
        this.displayname.product = this.getDisplayName(this.detailInfo.product)
      },

      handleEnvEdit(detailInfo) {
        if (detailInfo.pre_uuid) {
          const row = { ...detailInfo }
          this.$refs['envEdit'].showEdit(row)
        }
      },

      handleManage(row) {
        this.$refs['edit'].showEdit(this.queryForm.pre_uuid)
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.paginate()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.paginate()
      },

      paginate() {
        const startIndex = (this.queryForm.pageNo - 1) * this.queryForm.pageSize
        const endIndex = startIndex + this.queryForm.pageSize
        this.filteredList = this.list.slice(startIndex, endIndex)
      },

      handlePodSizeChange(val) {
        this.podForm.pageSize = val
        this.fetchPodData()
      },
      handlePodCurrentChange(val) {
        this.podForm.pageNum = val
        this.fetchPodData()
      },

      handleCommand(command) {
        if (command === 'll') {
          this.jenkins_drawer = true
        }
        if (command === 'cc') {
        }
      },
      handleNotImp() {
        this.$baseMessage('尚未实现，敬请期待', 'warning')
      },

      handleProgressiveText() {
        this.logloading = false
      },

      async handleGetbuildlog(row) {
        const { data } = await getPreJenkinsBuildInfo(row.pre_uuid, row)
        if (data) {
          this.jenkinsInfo.project = data.jks_job_name
          this.jenkinsInfo.buildno = data.job_id
          this.jenkins_drawer = true
        } else {
          this.$baseMessage(
            '未获取到构建信息，请稍后重试或重新加载页面',
            'error'
          )
        }
      },

      dateFormat: function (row) {
        const date = new Date(row.status.startTime)
        const year = date.getFullYear()
        const month =
          date.getMonth() + 1 < 10
            ? '0' + (date.getMonth() + 1)
            : date.getMonth() + 1
        const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
        const hours =
          date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
        const minutes =
          date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
        const seconds =
          date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
        return (
          year +
          '-' +
          month +
          '-' +
          day +
          ' ' +
          hours +
          ':' +
          minutes +
          ':' +
          seconds
        )
      },

      async handleDelete(val) {
        this.$baseConfirm(
          '删除服务会将该服务恢复至初始状态并删除全部构建记录，确认吗？',
          null,
          async () => {
            this.$set(val, 'pre_uuid', this.queryForm.pre_uuid)
            const { msg } = await deleteService(val.pre_uuid, val.project_id)
            setTimeout(() => {
              this.$baseMessage(msg, 'success')
              this.fetchData()
            }, 300)
          }
        )
      },

      async handleBuild(val) {
        this.$baseConfirm(
          '构建服务可能会导致测试中断，确认继续吗？',
          null,
          async () => {
            try {
              const param = this.generateParam(val)
              const msg = await this.executeBuild(param)

              const currentKeyword = this.queryForm.keyword
              this.displayMessage(msg, currentKeyword)
            } catch (error) {
              let errorMessage = '未知错误'
              if (error.response) {
                errorMessage = `服务错误: ${error.response.statusText}`
              } else if (error.request) {
                errorMessage = '网络错误，请检查网络连接'
              } else {
                errorMessage = error.message || '未知错误'
              }

              this.$baseMessage(errorMessage, 'error')
              throw new Error(errorMessage)
            }
          }
        )
      },

      generateParam(val) {
        if (
          !val.project &&
          (!this.selectRows || this.selectRows.length === 0)
        ) {
          throw new Error('未选择任何项目，请选择后再操作')
        }

        if (val.project) {
          this.$set(val, 'pre_uuid', this.queryForm.pre_uuid)
          return {
            project_id: `${val.project_id}`,
            project_branch: val.project_branch,
            name: val.name,
            appname: val.appname,
            pre_uuid: val.pre_uuid,
          }
        } else {
          const projects = this.selectRows.map((item) => item.project_id).join()
          const name = this.selectRows.map((item) => item.name).join()
          const appname = this.selectRows.map((item) => item.appname).join()
          const projectBranch = this.selectRows[0]?.project_branch || ''
          return {
            project_id: projects,
            name: name,
            appname: appname,
            pre_uuid: this.queryForm.pre_uuid,
            project_branch: projectBranch,
          }
        }
      },

      async executeBuild(param) {
        const { msg } = await putService(param.pre_uuid, param)
        return msg
      },

      displayMessage(msg, keyword) {
        this.$baseMessage(msg, 'success')

        this.fetchData().then(() => {
          if (keyword) {
            this.queryForm.keyword = keyword
            this.queryData()
          }
        })
      },

      async handleRestart(row) {
        this.$baseConfirm(
          '重启实例可能会导致测试中断，确认继续吗？',
          null,
          async () => {
            const param =
              '/namespaces/' +
              this.podForm.namespace +
              '/pods/' +
              row.metadata.name
            if (this.envType === '压力测试') {
              const { metadata } = await perfPodDelete(param)
            } else {
              const { metadata } = await sandboxPodDelete(param)
            }
            setTimeout(() => {
              this.$baseMessage(
                `pod重启成功，新实例${metadata.name}已启动。`,
                'success'
              )
              this.fetchPodData()
            }, 500)
          }
        )
      },
      handleRemote(row) {
        let baseurl
        if (this.envType === '压力测试') {
          baseurl =
            '/kubepi/dashboard/terminal?cluster=perf-test&type=terminal&'
        } else {
          baseurl = '/kubepi/dashboard/terminal?cluster=dev-test&type=terminal&'
        }

        const param =
          'namespace=' +
          this.podForm.namespace +
          '&pod=' +
          row.metadata.name +
          '&container=' +
          row.status.containerStatuses[0].name
        window.open(baseurl + param)
      },
      handleRemoteLog(row) {
        let baseurl
        if (this.envType === '压力测试') {
          baseurl = '/kubepi/dashboard/terminal?cluster=perf-test&type=log&'
        } else {
          baseurl = '/kubepi/dashboard/terminal?cluster=dev-test&type=log&'
        }
        const param =
          'namespace=' +
          this.podForm.namespace +
          '&pod=' +
          row.metadata.name +
          '&container=' +
          row.status.containerStatuses[0].name
        window.open(baseurl + param)
      },
      queryPods() {
        this.podForm.pageNum = 1
        this.fetchPodData()
      },

      setSelectRows(val) {
        this.selectRows = val
      },

      handleSortChange({ column, prop, order }) {
        if (prop === 'latest_build_time') {
          if (order === 'ascending') {
            this.filteredList.sort((a, b) => {
              return (
                new Date(a.latest_build_time) - new Date(b.latest_build_time)
              )
            })
          } else if (order === 'descending') {
            this.filteredList.sort((a, b) => {
              return (
                new Date(b.latest_build_time) - new Date(a.latest_build_time)
              )
            })
          }
        } else if (prop === 'publish_count') {
          if (order === 'ascending') {
            this.filteredList.sort((a, b) => a.publish_count - b.publish_count)
          } else if (order === 'descending') {
            this.filteredList.sort((a, b) => b.publish_count - a.publish_count)
          }
        }
      },

      handleCompare() {
        this.$router.push({
          name: 'configCenter',
          params: {
            pre_uuid: this.queryForm.pre_uuid,
          },
        })
      },
      handleWeak() {
        this.$router.push({
          name: 'weakNetTest',
          params: {
            pre_uuid: this.queryForm.pre_uuid,
          },
        })
      },
      handleTabchange() {
        if (this.podlist.length === 0) {
          if (this.activeTab === 'pods') {
            this.fetchPodData()
          }
        }
      },
      handleDrawerOpen() {
        this.fetchJksData()
        clearInterval(this.timer)
        this.timer = null
        this.setTimer()
        this.logloading = true
      },
      handleDrawerClose() {
        clearInterval(this.timer)
        this.logloading = false
        this.timer = null
        this.jenkinsInfo.start = 0
        this.progressiveText = ['构建任务排队中……']
      },

      setTimer() {
        if (this.timer == null) {
          this.timer = setInterval(() => {
            this.fetchJksData()
          }, 2500)
        }
      },
      scrollToBottom() {
        const container = this.$el.querySelector('.el-drawer__body')
        container.scrollTop = container.scrollHeight
      },

      async fetchJksData() {
        const param =
          'project=' +
          this.jenkinsInfo.project +
          '&buildno=' +
          this.jenkinsInfo.buildno +
          '&start=' +
          this.jenkinsInfo.start
        this.scrollToBottom()
        const resp = await getJksProgressiveText(param)
        if (resp) {
          this.progressiveText =
            this.progressiveText[0] === '构建任务排队中……'
              ? []
              : this.progressiveText
        }
        if (
          this.jenkinsInfo.start < parseInt(resp.headers['x-text-size']) ||
          resp.headers['x-more-data'] === 'true'
        ) {
          this.jenkinsInfo.start = parseInt(resp.headers['x-text-size'])
          this.progressiveText.push(resp.data)
        } else {
          clearInterval(this.timer)
          this.$baseNotify(
            '「' +
              this.jenkinsInfo.project +
              '」构建任务已结束，请查看日志结果。',
            '通知',
            'success',
            'bottom-right'
          )
          this.logloading = false
          this.timer = null
        }
        this.scrollToBottom()
      },

      queryData() {
        this.queryForm.pageNo = 1
        this.queryForm.pageSize = 10
        this.searchList()
      },

      handleRefresh() {
        this.fetchData()
      },

      searchList() {
        const searchKey = this.queryForm.keyword.toLowerCase()
        if (searchKey) {
          this.list = this.originalList.filter((item) => {
            const searchFields = ['name', 'project']
            return searchFields.some((field) => {
              const value = item[field]
              if (!value) return false
              return String(value).toLowerCase().includes(searchKey)
            })
          })
        } else {
          this.list = this.originalList
        }
        this.total = this.list.length
        this.paginate()
      },

      async fetchData() {
        try {
          const [services, detail] = await Promise.all([
            this.getServices(),
            this.getEnvDetail(),
          ])

          this.list = services.listData || []
          this.originalList = [...this.list]
          this.total = services.totalCount || 0
          this.paginate()
          this.listLoading = false
          this.detailInfo = detail.detailData[0] || {}
          await this.getLogsList()
        } catch (error) {
          console.error('请求失败:', error)
          await this.getLogsList()
          this.listLoading = false
        }
      },

      getServices() {
        this.listLoading = true
        return getServices(this.queryForm.pre_uuid)
          .then((response) => {
            return {
              listData: response.data,
              totalCount: response.data ? response.data.length : 0,
            }
          })
          .catch(() => {
            return { listData: [], totalCount: 0 }
          })
      },

      getEnvDetail() {
        return getEnv(this.queryForm.pre_uuid).then((res) => {
          return {
            detailData: res.data,
          }
        })
      },

      async getPreGray() {
        const { data } = await EnvRule(this.queryForm.pre_uuid)
        if (data) {
          this.albumid = data[0].albumid
        }
      },

      async getTraffic() {
        this.loading = true
        const startTime = Date.now()

        try {
          const { data, time_range } = await getPreTraffic(
            this.queryForm.pre_uuid
          )
          if (data) {
            this.preUuidTraffic = data
          }
          this.TrafficTimeRange = time_range
        } catch (error) {
          console.error('获取流量数据失败:', error)
        } finally {
          const duration = Date.now() - startTime
          const closeDelay = duration < 1000 ? 1000 : 0

          setTimeout(() => {
            this.loading = false
          }, closeDelay)
        }
      },

      async fetchPodData() {
        this.podLoading = true
        const param =
          'search=true&pageNum=' +
          this.podForm.pageNum +
          '&pageSize=' +
          this.podForm.pageSize +
          '&namespace=' +
          this.podForm.namespace +
          '&keywords=' +
          this.podForm.keyword

        let items
        let total
        if (this.envType === '压力测试') {
          items, (total = await perfPodlist(param))
        } else {
          items, (total = await sandboxPodlist(param))
        }

        this.podlist = items
        this.podtotal = total
        setTimeout(() => {
          this.podLoading = false
        }, 500)
      },

      openDialog() {
        this.aliasDialogVisible = true
      },
      closeDialog() {
        this.aliasDialogVisible = false
      },
      resetForm() {
        this.queryForm = { ...this.queryFormBackup }
        this.aliasDialogVisible = false
      },

      async getLogsList() {
        const { data: envLogs } = await EnvServieLogs(this.queryForm.pre_uuid)
        this.activities = envLogs.filter(this.isServiceActivity)

        this.$nextTick(() => {
          if (this.$refs.seamlessScroll) {
            this.$refs.seamlessScroll.reset()
          }
        })
      },

      getKibanaUrl(row) {
        const baseUrl =
          'https://kib.in.szwego.com/app/kibana#/discover/4efe2cd0-0f43-11ee-9f2a-edd22f300423'
        const timeParams =
          '_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))'
        const queryParams = `_a=(columns:!(kubernetes.namespace,kubernetes.labels.k8s-app,kubernetes.pod.name,message),filters:!(),index:'86cf5bd0-4a69-11eb-95e9-4173adb37bf2',interval:auto,query:(language:kuery,query:'kubernetes.container.name%20:%20%22${row.appname}%22%20and%20kubernetes.namespace%20:%20%22${row.pre_uuid}%22'),sort:!())`

        return `${baseUrl}?${timeParams}&${queryParams}`
      },

      getServiceUrl(row) {
        return `http://${row.appname}.${row.pre_uuid}.preprod.szwego.com`
      },

      isServiceActivity(activity) {
        return (
          activity.step_name &&
          (activity.step_name.includes('服务') ||
            activity.step_name.includes('构建'))
        )
      },
    },
  }
</script>

<style lang="scss" scoped>
  .presm-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909449 !important;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }

        .el-alert__content,
        .el-alert__title,
        .ci-alert-list {
          color: #909449;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 305px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }

    .ci-alert-list {
      margin-top: 5px;
    }
  }

  .block-span {
    display: block;
  }

  .traffic-card {
    ::v-deep .el-card__header {
      border-bottom: none;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .refresh-container {
        display: flex;
        align-items: center;
      }
    }

    height: 215px;
    display: flex;
    flex-direction: column;

    .body-div {
      flex-grow: 1;
      display: flex;
      flex-direction: column;

      .traffic-el-row {
        padding: 8px !important;
        justify-content: space-between;
        display: flex;

        span {
          align-items: center;
          justify-content: center;
          font-size: 50px;
          margin: 10px 10px;
          color: rgba(232, 60, 41, 0.64);
        }
      }

      ::v-deep .el-empty__image {
        display: none;
      }

      el-empty {
        flex-grow: 1;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .card-iframe {
    ::v-deep .el-card__body {
      padding: 10px;
    }

    .div-iframe {
      justify-content: center;
      align-items: center;
      padding: 10px;

      iframe {
        width: 600px;
        height: 200px;
      }
    }
  }

  .scroll-content {
    overflow: auto;
    padding: 10px;
    position: relative;
    height: 485px;
    display: flex;

    .dynamic-info {
      ::v-deep .seamless-scroll__item {
        width: auto;
      }
    }
  }

  .album-info-container {
    overflow: hidden;

    ::v-deep {
      .el-form {
        height: 100%;
      }
    }

    .clearfix {
      height: 5px;
    }

    .albumid-content {
      height: 150px;
    }
  }

  .latest-dynamics {
    height: 500px;

    ::v-deep .el-card__header {
      padding: 15px 20px;
      border-bottom: 2px solid #ebeef5;
    }

    ::v-deep .el-card__body {
      padding: 10px 10px;
    }

    .card-header {
      font-size: 14px;
    }

    .scroll-content {
      height: calc(100% - 55px);
      overflow: hidden;

      .dynamic-info {
        height: 100%;

        .el-timeline {
          padding-left: 5px;

          .el-timeline-item {
            padding-bottom: 15px;
            margin-bottom: 0;

            .el-timeline-item__timestamp {
              font-size: 11px;
              color: #909399;
            }

            .activity-line {
              font-size: 12px;
              line-height: 1.2;
              white-space: normal;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              line-clamp: 3;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              max-height: 3.6em;

              span {
                display: inline-block;
                margin: 2px 0;
              }
            }
          }
        }
      }
    }
  }
</style>
